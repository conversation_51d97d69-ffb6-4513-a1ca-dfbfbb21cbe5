# OSX
#
.DS_Store

# XDE
.expo/

# VSCode
.vscode/
.history/
jsconfig.json

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace
*/**/Pods

# Android/IJ
#
.classpath
.cxx
.gradle
.idea
.project
.settings
local.properties
android.iml

# Cocoapods
#
example/ios/Pods

# node.js
#
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
yarn.lock
.yarn/
example/.yarn/

# BUCK
buck-out/
\.buckd/
android/app/libs
android/keystores/debug.keystore

# Expo
.expo/*

# generated by bob
lib/
example/yarn.lock
yarn.lock
