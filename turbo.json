{"$schema": "https://turbo.build/schema.json", "pipeline": {"build:android": {"inputs": ["package.json", "android", "!android/build", "src/*.ts", "src/*.tsx", "example/package.json", "example/android", "!example/android/.gradle", "!example/android/build", "!example/android/app/build"], "outputs": []}, "build:ios": {"inputs": ["package.json", "*.podsp<PERSON>", "ios", "src/*.ts", "src/*.tsx", "example/package.json", "example/ios", "!example/ios/build", "!example/ios/Pods"], "outputs": []}}}