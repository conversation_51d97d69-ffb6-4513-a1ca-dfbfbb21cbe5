{"name": "@functionland/react-native-fula", "version": "1.55.13", "description": "This package is a bridge to use the Fula libp2p protocols in the react-native which is using wnfs", "type": "module", "main": "lib/commonjs/index", "module": "lib/module/index", "types": "lib/typescript/index.d.ts", "react-native": "src/index", "source": "src/index", "files": ["src", "lib", "android", "ios", "cpp", "*.podsp<PERSON>", "!lib/typescript/example", "!ios/build", "!android/build", "!android/gradle", "!android/gradlew", "!android/gradlew.bat", "!android/local.properties", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__", "!**/.*"], "scripts": {"test": "jest", "typecheck": "tsc --noEmit", "lint": "eslint \"**/*.{js,ts,tsx}\"", "prepack": "bob build", "release": "release-it", "example": "yarn --cwd example", "build:android": "cd example/android && ./gradlew assembleDebug --no-daemon --console=plain -PreactNativeArchitectures=arm64-v8a", "build:ios": "cd example/ios && xcodebuild -workspace FulaExample.xcworkspace -scheme FulaExample -configuration Debug -sdk iphonesimulator CC=clang CPLUSPLUS=clang++ LD=clang LDPLUSPLUS=clang++ GCC_OPTIMIZATION_LEVEL=0 GCC_PRECOMPILE_PREFIX_HEADER=YES ASSETCATALOG_COMPILER_OPTIMIZATION=time DEBUG_INFORMATION_FORMAT=dwarf COMPILER_INDEX_STORE_ENABLE=NO", "bootstrap": "yarn example && yarn install && yarn example pods", "clean": "del-cli android/build example/android/build example/android/app/build example/ios/build", "build:polkadot": "yarn generate:defs && yarn generate:meta", "generate:defs": "node --loader ts-node/esm node_modules/.bin/polkadot-types-from-defs --package fula-polkadotjs-typegen/interfaces --input ./src/interfaces --endpoint ./edgeware.json", "generate:meta": "node --loader ts-node/esm node_modules/.bin/polkadot-types-from-chain --package fula-polkadotjs-typegen/interfaces --endpoint ./edgeware.json --output ./src/interfaces"}, "keywords": ["react-native", "ios", "android"], "repository": "https://github.com/functionland/react-native-fula", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/ehsan6sha)", "license": "MIT", "bugs": {"url": "https://github.com/functionland/react-native-fula/issues"}, "homepage": "https://github.com/functionland/react-native-fula#readme", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@babel/plugin-proposal-export-namespace-from": "^7.16.7", "@commitlint/config-conventional": "^17.0.2", "@evilmartians/lefthook": "^1.2.2", "@polkadot/typegen": "^15.0.1", "@react-native-community/eslint-config": "^3.0.2", "@release-it/conventional-changelog": "^9.0.3", "@types/jest": "^28.1.2", "@types/react": "~17.0.21", "@types/react-native": "0.73.0", "commitlint": "^17.0.2", "del-cli": "^5.0.0", "eslint": "^8.4.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.2.5", "metro-react-native-babel-preset": "^0.77.0", "pod-install": "^0.1.0", "prettier": "^2.0.5", "react": "18.2.0", "react-native": "0.73.11", "react-native-builder-bob": "^0.20.0", "release-it": "^17.10.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.1", "turbo": "^1.10.7", "typescript": "^5.7.2"}, "resolutions": {"@types/react": "17.0.21"}, "peerDependencies": {"@babel/core": "^7.0.0-0", "@babel/preset-env": "^7.1.6", "react": "*", "react-native": "0.73.11"}, "engines": {"node": ">= 16.0.0"}, "packageManager": "yarn@1.22.21", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular"}}}, "eslintConfig": {"root": true, "extends": ["@react-native-community", "prettier"], "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "eslintIgnore": ["node_modules/", "lib/"], "prettier": {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", ["typescript", {"project": "tsconfig.build.json"}]]}, "dependencies": {"@polkadot/api": "^15.0.1", "@polkadot/keyring": "^13.2.3", "@polkadot/typegen": "^15.0.1", "@polkadot/util": "^13.2.3", "@polkadot/util-crypto": "^13.2.3", "text-encoding": "^0.7.0", "yarn": "^1.22.21"}}