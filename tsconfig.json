{
  "compilerOptions": {
    "allowImportingTsExtensions": true,
    "noEmit": true,  // Required when using allowImportingTsExtensions
    "baseUrl": "./",
    "paths": {
      "react-native-fula": ["./src/index"],
      "fula-polkadotjs-typegen/*": ["./src/*"],
      "@polkadot/types/lookup": ["./src/interfaces/types-lookup.ts"],
      "@polkadot/api/augment": ["./src/interfaces/augment-api.ts"],
      "@polkadot/types/augment": ["./src/interfaces/augment-types.ts"]
    },
    "allowUnreachableCode": false,
    "allowUnusedLabels": false,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "jsx": "react",
    "lib": ["esnext"],
    "module": "esnext",
    "moduleResolution": "node",
    "noFallthroughCasesInSwitch": true,
    "noImplicitReturns": true,
    "noImplicitUseStrict": false,
    "noStrictGenericChecks": false,
    "noUncheckedIndexedAccess": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "strict": true,
    "target": "esnext",
    "verbatimModuleSyntax": true
  }
}
