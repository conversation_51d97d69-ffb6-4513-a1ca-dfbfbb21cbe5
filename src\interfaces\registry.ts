// Auto-generated via `yarn polkadot-types-from-defs`, do not edit
/* eslint-disable */

// import type lookup before we augment - in some environments
// this is required to allow for ambient/previous definitions
import '@polkadot/types/types/registry';

import type { FinalityGrandpaEquivocationPrecommit, FinalityGrandpaEquivocationPrevote, FinalityGrandpaPrecommit, FinalityGrandpaPrevote, FrameSupportDispatchDispatchClass, FrameSupportDispatchDispatchInfo, FrameSupportDispatchPays, FrameSupportDispatchPerDispatchClassU32, FrameSupportDispatchPerDispatchClassWeight, FrameSupportDispatchPerDispatchClassWeightsPerClass, FrameSupportDispatchRawOrigin, FrameSupportPalletId, FrameSupportPreimagesBounded, FrameSupportTokensMiscBalanceStatus, FrameSystemAccountInfo, FrameSystemCall, FrameSystemError, FrameSystemEvent, FrameSystemEventRecord, FrameSystemExtensionsCheckGenesis, FrameSystemExtensionsCheckNonZeroSender, FrameSystemExtensionsCheckNonce, FrameSystemExtensionsCheckSpecVersion, FrameSystemExtensionsCheckTxVersion, FrameSystemExtensionsCheckWeight, FrameSystemLastRuntimeUpgradeInfo, FrameSystemLimitsBlockLength, FrameSystemLimitsBlockWeights, FrameSystemLimitsWeightsPerClass, FrameSystemPhase, FulaPoolCall, FulaPoolError, FulaPoolEvent, FulaPoolPool, FulaPoolPoolRequest, FulaPoolUser, FunctionlandFulaCall, FunctionlandFulaChallenge, FunctionlandFulaChallengeState, FunctionlandFulaClaimData, FunctionlandFulaError, FunctionlandFulaEvent, FunctionlandFulaManifest, FunctionlandFulaManifestAvailable, FunctionlandFulaManifestStorageData, FunctionlandFulaManifestWithPoolId, FunctionlandFulaStorerData, FunctionlandFulaUploaderData, PalletBalancesAccountData, PalletBalancesBalanceLock, PalletBalancesCall, PalletBalancesError, PalletBalancesEvent, PalletBalancesIdAmount, PalletBalancesReasons, PalletBalancesReserveData, PalletCollectiveCall, PalletCollectiveError, PalletCollectiveEvent, PalletCollectiveRawOrigin, PalletCollectiveVotes, PalletGrandpaCall, PalletGrandpaError, PalletGrandpaEvent, PalletGrandpaStoredPendingChange, PalletGrandpaStoredState, PalletImOnlineBoundedOpaqueNetworkState, PalletImOnlineCall, PalletImOnlineError, PalletImOnlineEvent, PalletImOnlineHeartbeat, PalletImOnlineSr25519AppSr25519Public, PalletImOnlineSr25519AppSr25519Signature, PalletSchedulerCall, PalletSchedulerError, PalletSchedulerEvent, PalletSchedulerScheduled, PalletSessionCall, PalletSessionError, PalletSessionEvent, PalletSudoCall, PalletSudoError, PalletSudoEvent, PalletTimestampCall, PalletTransactionPaymentChargeTransactionPayment, PalletTransactionPaymentEvent, PalletTransactionPaymentReleases, PalletUtilityCall, PalletUtilityError, PalletUtilityEvent, SpArithmeticArithmeticError, SpConsensusAuraSr25519AppSr25519Public, SpConsensusGrandpaAppPublic, SpConsensusGrandpaAppSignature, SpConsensusGrandpaEquivocation, SpConsensusGrandpaEquivocationProof, SpCoreCryptoKeyTypeId, SpCoreEcdsaSignature, SpCoreEd25519Public, SpCoreEd25519Signature, SpCoreOffchainOpaqueNetworkState, SpCoreSr25519Public, SpCoreSr25519Signature, SpCoreVoid, SpRuntimeDigest, SpRuntimeDigestDigestItem, SpRuntimeDispatchError, SpRuntimeModuleError, SpRuntimeMultiSignature, SpRuntimeTokenError, SpRuntimeTransactionalError, SpVersionRuntimeVersion, SpWeightsRuntimeDbWeight, SpWeightsWeightV2Weight, SugarfungeAssetAsset, SugarfungeAssetCall, SugarfungeAssetClass, SugarfungeAssetError, SugarfungeAssetEvent, SugarfungeBagBag, SugarfungeBagBagClass, SugarfungeBagCall, SugarfungeBagError, SugarfungeBagEvent, SugarfungeBundleBundle, SugarfungeBundleCall, SugarfungeBundleError, SugarfungeBundleEvent, SugarfungeDaoCall, SugarfungeDaoError, SugarfungeDaoEvent, SugarfungeExgineCall, SugarfungeExgineError, SugarfungeExgineEvent, SugarfungeMarketAmm, SugarfungeMarketAmountOp, SugarfungeMarketAssetRate, SugarfungeMarketCall, SugarfungeMarketError, SugarfungeMarketEvent, SugarfungeMarketMarket, SugarfungeMarketRateAccount, SugarfungeMarketRateAction, SugarfungeMarketRateBalance, SugarfungeRuntimeOpaqueSessionKeys, SugarfungeRuntimeOriginCaller, SugarfungeRuntimeRuntime, SugarfungeValidatorSetCall, SugarfungeValidatorSetError, SugarfungeValidatorSetEvent } from '@polkadot/types/lookup';

declare module '@polkadot/types/types/registry' {
  interface InterfaceTypes {
    FinalityGrandpaEquivocationPrecommit: FinalityGrandpaEquivocationPrecommit;
    FinalityGrandpaEquivocationPrevote: FinalityGrandpaEquivocationPrevote;
    FinalityGrandpaPrecommit: FinalityGrandpaPrecommit;
    FinalityGrandpaPrevote: FinalityGrandpaPrevote;
    FrameSupportDispatchDispatchClass: FrameSupportDispatchDispatchClass;
    FrameSupportDispatchDispatchInfo: FrameSupportDispatchDispatchInfo;
    FrameSupportDispatchPays: FrameSupportDispatchPays;
    FrameSupportDispatchPerDispatchClassU32: FrameSupportDispatchPerDispatchClassU32;
    FrameSupportDispatchPerDispatchClassWeight: FrameSupportDispatchPerDispatchClassWeight;
    FrameSupportDispatchPerDispatchClassWeightsPerClass: FrameSupportDispatchPerDispatchClassWeightsPerClass;
    FrameSupportDispatchRawOrigin: FrameSupportDispatchRawOrigin;
    FrameSupportPalletId: FrameSupportPalletId;
    FrameSupportPreimagesBounded: FrameSupportPreimagesBounded;
    FrameSupportTokensMiscBalanceStatus: FrameSupportTokensMiscBalanceStatus;
    FrameSystemAccountInfo: FrameSystemAccountInfo;
    FrameSystemCall: FrameSystemCall;
    FrameSystemError: FrameSystemError;
    FrameSystemEvent: FrameSystemEvent;
    FrameSystemEventRecord: FrameSystemEventRecord;
    FrameSystemExtensionsCheckGenesis: FrameSystemExtensionsCheckGenesis;
    FrameSystemExtensionsCheckNonZeroSender: FrameSystemExtensionsCheckNonZeroSender;
    FrameSystemExtensionsCheckNonce: FrameSystemExtensionsCheckNonce;
    FrameSystemExtensionsCheckSpecVersion: FrameSystemExtensionsCheckSpecVersion;
    FrameSystemExtensionsCheckTxVersion: FrameSystemExtensionsCheckTxVersion;
    FrameSystemExtensionsCheckWeight: FrameSystemExtensionsCheckWeight;
    FrameSystemLastRuntimeUpgradeInfo: FrameSystemLastRuntimeUpgradeInfo;
    FrameSystemLimitsBlockLength: FrameSystemLimitsBlockLength;
    FrameSystemLimitsBlockWeights: FrameSystemLimitsBlockWeights;
    FrameSystemLimitsWeightsPerClass: FrameSystemLimitsWeightsPerClass;
    FrameSystemPhase: FrameSystemPhase;
    FulaPoolCall: FulaPoolCall;
    FulaPoolError: FulaPoolError;
    FulaPoolEvent: FulaPoolEvent;
    FulaPoolPool: FulaPoolPool;
    FulaPoolPoolRequest: FulaPoolPoolRequest;
    FulaPoolUser: FulaPoolUser;
    FunctionlandFulaCall: FunctionlandFulaCall;
    FunctionlandFulaChallenge: FunctionlandFulaChallenge;
    FunctionlandFulaChallengeState: FunctionlandFulaChallengeState;
    FunctionlandFulaClaimData: FunctionlandFulaClaimData;
    FunctionlandFulaError: FunctionlandFulaError;
    FunctionlandFulaEvent: FunctionlandFulaEvent;
    FunctionlandFulaManifest: FunctionlandFulaManifest;
    FunctionlandFulaManifestAvailable: FunctionlandFulaManifestAvailable;
    FunctionlandFulaManifestStorageData: FunctionlandFulaManifestStorageData;
    FunctionlandFulaManifestWithPoolId: FunctionlandFulaManifestWithPoolId;
    FunctionlandFulaStorerData: FunctionlandFulaStorerData;
    FunctionlandFulaUploaderData: FunctionlandFulaUploaderData;
    PalletBalancesAccountData: PalletBalancesAccountData;
    PalletBalancesBalanceLock: PalletBalancesBalanceLock;
    PalletBalancesCall: PalletBalancesCall;
    PalletBalancesError: PalletBalancesError;
    PalletBalancesEvent: PalletBalancesEvent;
    PalletBalancesIdAmount: PalletBalancesIdAmount;
    PalletBalancesReasons: PalletBalancesReasons;
    PalletBalancesReserveData: PalletBalancesReserveData;
    PalletCollectiveCall: PalletCollectiveCall;
    PalletCollectiveError: PalletCollectiveError;
    PalletCollectiveEvent: PalletCollectiveEvent;
    PalletCollectiveRawOrigin: PalletCollectiveRawOrigin;
    PalletCollectiveVotes: PalletCollectiveVotes;
    PalletGrandpaCall: PalletGrandpaCall;
    PalletGrandpaError: PalletGrandpaError;
    PalletGrandpaEvent: PalletGrandpaEvent;
    PalletGrandpaStoredPendingChange: PalletGrandpaStoredPendingChange;
    PalletGrandpaStoredState: PalletGrandpaStoredState;
    PalletImOnlineBoundedOpaqueNetworkState: PalletImOnlineBoundedOpaqueNetworkState;
    PalletImOnlineCall: PalletImOnlineCall;
    PalletImOnlineError: PalletImOnlineError;
    PalletImOnlineEvent: PalletImOnlineEvent;
    PalletImOnlineHeartbeat: PalletImOnlineHeartbeat;
    PalletImOnlineSr25519AppSr25519Public: PalletImOnlineSr25519AppSr25519Public;
    PalletImOnlineSr25519AppSr25519Signature: PalletImOnlineSr25519AppSr25519Signature;
    PalletSchedulerCall: PalletSchedulerCall;
    PalletSchedulerError: PalletSchedulerError;
    PalletSchedulerEvent: PalletSchedulerEvent;
    PalletSchedulerScheduled: PalletSchedulerScheduled;
    PalletSessionCall: PalletSessionCall;
    PalletSessionError: PalletSessionError;
    PalletSessionEvent: PalletSessionEvent;
    PalletSudoCall: PalletSudoCall;
    PalletSudoError: PalletSudoError;
    PalletSudoEvent: PalletSudoEvent;
    PalletTimestampCall: PalletTimestampCall;
    PalletTransactionPaymentChargeTransactionPayment: PalletTransactionPaymentChargeTransactionPayment;
    PalletTransactionPaymentEvent: PalletTransactionPaymentEvent;
    PalletTransactionPaymentReleases: PalletTransactionPaymentReleases;
    PalletUtilityCall: PalletUtilityCall;
    PalletUtilityError: PalletUtilityError;
    PalletUtilityEvent: PalletUtilityEvent;
    SpArithmeticArithmeticError: SpArithmeticArithmeticError;
    SpConsensusAuraSr25519AppSr25519Public: SpConsensusAuraSr25519AppSr25519Public;
    SpConsensusGrandpaAppPublic: SpConsensusGrandpaAppPublic;
    SpConsensusGrandpaAppSignature: SpConsensusGrandpaAppSignature;
    SpConsensusGrandpaEquivocation: SpConsensusGrandpaEquivocation;
    SpConsensusGrandpaEquivocationProof: SpConsensusGrandpaEquivocationProof;
    SpCoreCryptoKeyTypeId: SpCoreCryptoKeyTypeId;
    SpCoreEcdsaSignature: SpCoreEcdsaSignature;
    SpCoreEd25519Public: SpCoreEd25519Public;
    SpCoreEd25519Signature: SpCoreEd25519Signature;
    SpCoreOffchainOpaqueNetworkState: SpCoreOffchainOpaqueNetworkState;
    SpCoreSr25519Public: SpCoreSr25519Public;
    SpCoreSr25519Signature: SpCoreSr25519Signature;
    SpCoreVoid: SpCoreVoid;
    SpRuntimeDigest: SpRuntimeDigest;
    SpRuntimeDigestDigestItem: SpRuntimeDigestDigestItem;
    SpRuntimeDispatchError: SpRuntimeDispatchError;
    SpRuntimeModuleError: SpRuntimeModuleError;
    SpRuntimeMultiSignature: SpRuntimeMultiSignature;
    SpRuntimeTokenError: SpRuntimeTokenError;
    SpRuntimeTransactionalError: SpRuntimeTransactionalError;
    SpVersionRuntimeVersion: SpVersionRuntimeVersion;
    SpWeightsRuntimeDbWeight: SpWeightsRuntimeDbWeight;
    SpWeightsWeightV2Weight: SpWeightsWeightV2Weight;
    SugarfungeAssetAsset: SugarfungeAssetAsset;
    SugarfungeAssetCall: SugarfungeAssetCall;
    SugarfungeAssetClass: SugarfungeAssetClass;
    SugarfungeAssetError: SugarfungeAssetError;
    SugarfungeAssetEvent: SugarfungeAssetEvent;
    SugarfungeBagBag: SugarfungeBagBag;
    SugarfungeBagBagClass: SugarfungeBagBagClass;
    SugarfungeBagCall: SugarfungeBagCall;
    SugarfungeBagError: SugarfungeBagError;
    SugarfungeBagEvent: SugarfungeBagEvent;
    SugarfungeBundleBundle: SugarfungeBundleBundle;
    SugarfungeBundleCall: SugarfungeBundleCall;
    SugarfungeBundleError: SugarfungeBundleError;
    SugarfungeBundleEvent: SugarfungeBundleEvent;
    SugarfungeDaoCall: SugarfungeDaoCall;
    SugarfungeDaoError: SugarfungeDaoError;
    SugarfungeDaoEvent: SugarfungeDaoEvent;
    SugarfungeExgineCall: SugarfungeExgineCall;
    SugarfungeExgineError: SugarfungeExgineError;
    SugarfungeExgineEvent: SugarfungeExgineEvent;
    SugarfungeMarketAmm: SugarfungeMarketAmm;
    SugarfungeMarketAmountOp: SugarfungeMarketAmountOp;
    SugarfungeMarketAssetRate: SugarfungeMarketAssetRate;
    SugarfungeMarketCall: SugarfungeMarketCall;
    SugarfungeMarketError: SugarfungeMarketError;
    SugarfungeMarketEvent: SugarfungeMarketEvent;
    SugarfungeMarketMarket: SugarfungeMarketMarket;
    SugarfungeMarketRateAccount: SugarfungeMarketRateAccount;
    SugarfungeMarketRateAction: SugarfungeMarketRateAction;
    SugarfungeMarketRateBalance: SugarfungeMarketRateBalance;
    SugarfungeRuntimeOpaqueSessionKeys: SugarfungeRuntimeOpaqueSessionKeys;
    SugarfungeRuntimeOriginCaller: SugarfungeRuntimeOriginCaller;
    SugarfungeRuntimeRuntime: SugarfungeRuntimeRuntime;
    SugarfungeValidatorSetCall: SugarfungeValidatorSetCall;
    SugarfungeValidatorSetError: SugarfungeValidatorSetError;
    SugarfungeValidatorSetEvent: SugarfungeValidatorSetEvent;
  } // InterfaceTypes
} // declare module
