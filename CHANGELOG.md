# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.55.14] - 2025-01-22

### Changed
- **BREAKING**: Updated Android target SDK to API level 35 (Android 15) to comply with Google Play requirements
- Updated Android compile SDK to API level 35
- Updated Android Gradle Plugin to version 8.7.3 for better compatibility with API level 35

### Added
- Added `joinPoolWithChain` method for joining pools on specific blockchain networks
- Added `leavePoolWithChain` method for leaving pools on specific blockchain networks
- Added comprehensive input validation and error handling for new chain-specific methods
- Added example usage for new chain-specific pool methods in example app

### Technical Details
- Apps using this library will now be compliant with Google Play's requirement for targeting Android 15 (API level 35)
- The deadline for this compliance is August 30, 2025
- All existing functionality remains unchanged, only the target SDK has been updated

## [1.55.13] - Previous Release
- Previous functionality and features
