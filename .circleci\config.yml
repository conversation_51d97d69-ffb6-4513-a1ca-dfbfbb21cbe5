version: 2.1

executors:
  default:
    docker:
      - image: circleci/node:10
    working_directory: ~/project

commands:
  attach_project:
    steps:
      - attach_workspace:
          at: ~/project

jobs:
  install-dependencies:
    executor: default
    steps:
      - checkout
      - attach_project
      - restore_cache:
          keys:
            - dependencies-{{ checksum "package.json" }}
            - dependencies-
      - restore_cache:
          keys:
            - dependencies-example-{{ checksum "example/package.json" }}
            - dependencies-example-
      - run:
          name: Install dependencies
          command: |
            yarn install --cwd example --frozen-lockfile
            yarn install --frozen-lockfile
      - save_cache:
          key: dependencies-{{ checksum "package.json" }}
          paths: node_modules
      - save_cache:
          key: dependencies-example-{{ checksum "example/package.json" }}
          paths: example/node_modules
      - persist_to_workspace:
          root: .
          paths: .

  lint:
    executor: default
    steps:
      - attach_project
      - run:
          name: Lint files
          command: |
            yarn lint

  typescript:
    executor: default
    steps:
      - attach_project
      - run:
          name: Typecheck files
          command: |
            yarn typescript

  unit-tests:
    executor: default
    steps:
      - attach_project
      - run:
          name: Run unit tests
          command: |
            yarn test --coverage
      - store_artifacts:
          path: coverage
          destination: coverage

  build-package:
    executor: default
    steps:
      - attach_project
      - run:
          name: Build package
          command: |
            yarn prepare

workflows:
  build-and-test:
    jobs:
      - install-dependencies
      - lint:
          requires:
            - install-dependencies
      - typescript:
          requires:
            - install-dependencies
      - unit-tests:
          requires:
            - install-dependencies
      - build-package:
          requires:
            - install-dependencies
